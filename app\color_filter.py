from dataclasses import dataclass, field
from typing import Literal

import numpy as np

from image_colorspace import Image, Mask


@dataclass
class ColorFilter:
    color_space: Literal["BGR", "LAB"] = "BGR"
    passthrough: list[bool] = field(default_factory=lambda: [False, False, False])
    lower: list[int] = field(default_factory=lambda: [0, 0, 0])
    upper: list[int] = field(default_factory=lambda: [255, 255, 255])
    invert: bool = False


def apply_color_filter(image: Image, filter: ColorFilter) -> Mask:
    image_space = image.BGR if filter.color_space == "BGR" else image.LAB
    mask = np.zeros(image_space.shape[:2], dtype=np.uint8)
    for i in range(3):
        if filter.passthrough[i]:
            mask |= (image_space[:, :, i] < filter.lower[i]) | (
                image_space[:, :, i] > filter.upper[i]
            )
        else:
            mask |= (image_space[:, :, i] > filter.lower[i]) & (
                image_space[:, :, i] < filter.upper[i]
            )
    if filter.invert:
        mask = ~mask
    mask *= 255
    return mask
